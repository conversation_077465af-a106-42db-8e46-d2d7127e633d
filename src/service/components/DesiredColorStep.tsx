import React, { useState, useRef } from 'react';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Zap, Eye, AlertTriangle, Sparkles } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { HairZone } from '@/types/hair-diagnosis';
import { DesiredPhoto, DesiredPhotoType } from '@/types/desired-photo';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { MaintenanceLevel, BudgetLevel } from '@/types/lifestyle-preferences';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';
import { usePhotoAnalysis } from '@/src/service/hooks/usePhotoAnalysis';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import { aggregateDesiredPhotoAnalyses } from '@/src/service/utils/serviceHelpers';
import { analyzeServiceViability } from '@/utils/viability-analyzer';

// Import existing components
import DesiredPhotoGallery from '@/components/DesiredPhotoGallery';
import DesiredColorAnalysisForm from '@/components/DesiredColorAnalysisForm';
import ViabilityIndicator from '@/components/ViabilityIndicator';
import { DiagnosisSummary } from '@/components/ui/DiagnosisSummary';
import AIResultNotification from '@/components/AIResultNotification';

interface DesiredColorStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack?: () => void;
  onSave?: () => void;
  onSaveSilent?: () => void;
}

// analyzeServiceViability is now imported from utils/viability-analyzer.ts

export const DesiredColorStep: React.FC<DesiredColorStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack: _onBack,
  onSave,
  onSaveSilent,
}) => {
  const [isAnalyzingDesired, setIsAnalyzingDesired] = useState(false);
  const [isRecalculatingViability, setIsRecalculatingViability] = useState(false);
  const [showAINotification, setShowAINotification] = useState(false);
  const [aiFieldsCount, setAIFieldsCount] = useState(0);

  // ScrollView ref for auto-scroll
  const scrollRef = useRef<ScrollView>(null);

  // Track desired capture step without causing re-renders
  const currentDesiredPhotoTypeRef = useRef<DesiredPhotoType>(DesiredPhotoType.MAIN);

  // Debounce timer para optimizar performance
  const viabilityRecalcTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Recalcular viabilidad automáticamente cuando el usuario edite cualquier valor relevante
  React.useEffect(() => {
    // Limpiar timeout anterior si existe
    if (viabilityRecalcTimeout.current) {
      clearTimeout(viabilityRecalcTimeout.current);
    }

    // Solo recalcular si tenemos los datos mínimos necesarios
    if (!data.desiredAnalysisResult || !data.hairThickness || !data.zoneColorAnalysis) {
      return;
    }

    // Debounce el cálculo para evitar cálculos excesivos mientras el usuario edita
    viabilityRecalcTimeout.current = setTimeout(() => {
      // Extraer valores clave para comparación
      const currentLevel = data.zoneColorAnalysis?.[HairZone.ROOTS]?.level || 6;
      const _desiredLevel =
        parseInt(data.desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;
      const _desiredTone = data.desiredAnalysisResult?.general?.overallTone;

      // Crear el diagnosis object
      const diagnosis = {
        hairThickness: data.hairThickness,
        hairDensity: data.hairDensity,
        overallTone: data.overallTone,
        overallReflect: data.overallReflect || data.overallUndertone,
        averageLevel: currentLevel,
        zoneAnalysis: data.zoneColorAnalysis,
        zonePhysicalAnalysis: data.zonePhysicalAnalysis,
      };

      // Calcular nueva viabilidad
      const newViabilityAnalysis = data.desiredAnalysisResult
        ? analyzeServiceViability(diagnosis, data.desiredAnalysisResult, data.zoneColorAnalysis)
        : null;

      // Solo actualizar si hay cambios significativos en la viabilidad
      const hasSignificantChange =
        newViabilityAnalysis &&
        (!data.viabilityAnalysis ||
          data.viabilityAnalysis.score !== newViabilityAnalysis.score ||
          data.viabilityAnalysis.factors.levelDifference !==
            newViabilityAnalysis.factors.levelDifference ||
          data.viabilityAnalysis.factors.hairHealth !== newViabilityAnalysis.factors.hairHealth);

      if (hasSignificantChange) {
        // Debug logging removed for production

        // Mostrar brevemente que se está recalculando
        setIsRecalculatingViability(true);

        onUpdate({ viabilityAnalysis: newViabilityAnalysis });

        // Ocultar indicador después de un breve delay
        setTimeout(() => setIsRecalculatingViability(false), 500);
      }
    }, 300); // Debounce de 300ms
  }, [
    // Dependencies específicas que disparan el recálculo
    data.desiredAnalysisResult?.general?.overallLevel,
    data.desiredAnalysisResult?.general?.overallTone,
    data.desiredAnalysisResult?.zones?.[HairZone.ROOTS]?.desiredLevel,
    data.desiredAnalysisResult?.zones?.[HairZone.MIDS]?.desiredLevel,
    data.desiredAnalysisResult?.zones?.[HairZone.ENDS]?.desiredLevel,
    data.zoneColorAnalysis,
    data.zonePhysicalAnalysis,
    data.hairThickness,
    data.hairDensity,
  ]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (viabilityRecalcTimeout.current) {
        clearTimeout(viabilityRecalcTimeout.current);
      }
    };
  }, []);

  // Track if we've shown the notification for current analysis
  const hasShownNotificationRef = useRef(false);

  // Show notification and scroll when AI analysis completes
  React.useEffect(() => {
    if (
      data.desiredAnalysisResult &&
      data.desiredAnalysisResult.isFromAI &&
      !hasShownNotificationRef.current &&
      scrollRef.current
    ) {
      // Count fields that were filled
      let fieldsCount = 0;
      if (data.desiredAnalysisResult.general?.overallTone) fieldsCount++;
      if (data.desiredAnalysisResult.general?.overallLevel) fieldsCount++;
      if (data.desiredAnalysisResult.general?.overallReflect) fieldsCount++;
      if (data.desiredAnalysisResult.zones) {
        fieldsCount += Object.keys(data.desiredAnalysisResult.zones).length * 2;
      }
      if (data.desiredAnalysisResult.compatibility) fieldsCount++;

      setAIFieldsCount(fieldsCount);
      setShowAINotification(true);
      hasShownNotificationRef.current = true;

      // Haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Smooth scroll to results section
      setTimeout(() => {
        scrollRef.current?.scrollTo({ y: 300, animated: true });
      }, 300);
    }

    // Reset the flag when there's no analysis result
    if (!data.desiredAnalysisResult) {
      hasShownNotificationRef.current = false;
    }
  }, [data.desiredAnalysisResult]);

  // AI Analysis Store
  const { analyzeDesiredPhoto } = useAIAnalysisStore();

  const {
    currentPhotoAngle: _currentPhotoAngle,
    setCurrentPhotoAngle: _setCurrentPhotoAngle,
    handleDesiredCameraCapture,
    takePhoto,
  } = usePhotoAnalysis();

  const handleDesiredPhotoAdd = (
    isCamera: boolean,
    useGuidedCapture?: boolean,
    photoType?: DesiredPhotoType
  ) => {
    if (isCamera) {
      // Usar cámara normal
      currentDesiredPhotoTypeRef.current = photoType || DesiredPhotoType.MAIN;
      handleDesiredCameraOpen();
    } else {
      // Usar galería de imágenes
      handleDesiredImagePick();
    }
  };

  const handleDesiredCameraNormal = () => {
    // Usar cámara normal (como en diagnóstico)
    currentDesiredPhotoTypeRef.current = DesiredPhotoType.MAIN;
    handleDesiredCameraOpen();
  };

  const handleDesiredImagePick = async () => {
    try {
      // Usar la función de selección múltiple adaptada para fotos deseadas
      await pickDesiredImages();
    } catch (error) {
      logger.error('Error picking desired images:', error);
    }
  };

  const handleDesiredCameraOpen = async () => {
    try {
      await takePhoto(async uri => {
        // Determinar el tipo de foto basado en las fotos existentes
        let photoType = DesiredPhotoType.MAIN;
        if (data.desiredPhotos.length === 1) {
          photoType = DesiredPhotoType.DETAILS;
        } else if (data.desiredPhotos.length >= 2) {
          photoType = DesiredPhotoType.ALTERNATIVE;
        }

        // Crear nueva foto deseada
        const newPhoto: DesiredPhoto = {
          id: Date.now().toString(),
          uri,
          type: photoType,
          timestamp: new Date(),
        };

        // Actualizar fotos
        const updatedPhotos = [...data.desiredPhotos, newPhoto];
        onUpdate({ desiredPhotos: updatedPhotos });
        // DISABLED: onSave to prevent excessive auto-saving during image processing
        // The interval-based auto-save will handle saving automatically
      });
    } catch (error) {
      logger.error('Error taking desired photo:', error);
    }
  };

  const pickDesiredImages = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsMultipleSelection: true,
        quality: 0.5,
        selectionLimit: 5 - data.desiredPhotos.length,
        base64: false,
      });

      if (!result.canceled && result.assets) {
        const newPhotos: DesiredPhoto[] = result.assets.map((asset, index) => {
          // Determinar el tipo de foto basado en el índice y fotos existentes
          let photoType = DesiredPhotoType.MAIN;
          const totalIndex = data.desiredPhotos.length + index;

          if (totalIndex === 0) {
            photoType = DesiredPhotoType.MAIN;
          } else if (totalIndex === 1) {
            photoType = DesiredPhotoType.DETAILS;
          } else {
            photoType = DesiredPhotoType.ALTERNATIVE;
          }

          return {
            id: Date.now().toString() + index,
            uri: asset.uri,
            type: photoType,
            timestamp: new Date(),
          };
        });

        // Actualizar fotos
        const updatedPhotos = [...data.desiredPhotos, ...newPhotos];
        onUpdate({ desiredPhotos: updatedPhotos });
        // DISABLED: onSave to prevent excessive auto-saving during image processing
        // The interval-based auto-save will handle saving automatically
      }
    } catch (error) {
      logger.error('Error al seleccionar imágenes:', error);
      Alert.alert('Error', 'No se pudo acceder a la galería');
    }
  };

  const handleManualModeSelect = () => {
    onUpdate({ desiredMethod: 'manual' });

    // Inicializar analysisResult si no existe
    if (!data.desiredAnalysisResult) {
      const initialResult: DesiredColorAnalysisResult = {
        general: {
          overallLevel: '',
          overallTone: '',
          technique: '',
          customTechnique: '',
        },
        zones: {
          [HairZone.ROOTS]: {
            zone: HairZone.ROOTS,
            desiredLevel: 0,
            desiredTone: '',
            desiredReflect: '',
            coverage: 100,
          },
          [HairZone.MIDS]: {
            zone: HairZone.MIDS,
            desiredLevel: 0,
            desiredTone: '',
            desiredReflect: '',
            coverage: 100,
          },
          [HairZone.ENDS]: {
            zone: HairZone.ENDS,
            desiredLevel: 0,
            desiredTone: '',
            desiredReflect: '',
            coverage: 100,
          },
        },
        advanced: {
          contrast: 'medium',
          direction: 'neutral',
          graysCoverage: 100,
          finalTexture: 'natural',
          specialNotes: '',
        },
        lifestyle: {
          maintenanceLevel: MaintenanceLevel.MEDIUM,
          avoidTones: [],
          budgetLevel: BudgetLevel.STANDARD,
        },
        confidence: 0,
        isFromAI: false,
      };

      onUpdate({ desiredAnalysisResult: initialResult });
    }
  };

  const analyzeDesiredPhotos = async () => {
    if (data.desiredPhotos.length === 0) {
      Alert.alert('Error', 'Por favor agrega al menos una foto de referencia');
      return;
    }

    if (!data.clientId) {
      Alert.alert('Error', 'No se encontró información del cliente. Por favor intenta de nuevo.');
      return;
    }

    setIsAnalyzingDesired(true);

    try {
      // Construir objeto diagnosis completo desde los datos del servicio
      const diagnosis = {
        hairThickness: data.hairThickness,
        hairDensity: data.hairDensity,
        overallTone: data.overallTone,
        overallReflect: data.overallReflect || data.overallUndertone,
        averageLevel: data.zoneColorAnalysis?.[HairZone.ROOTS]?.level || 6,
        zoneAnalysis: data.zoneColorAnalysis,
        zonePhysicalAnalysis: data.zonePhysicalAnalysis,
        detectedRisks: {
          metallic: false,
          henna: false,
          damaged:
            data.zonePhysicalAnalysis?.[HairZone.ROOTS]?.damage === 'Alto' ||
            data.zonePhysicalAnalysis?.[HairZone.MIDS]?.damage === 'Alto' ||
            data.zonePhysicalAnalysis?.[HairZone.ENDS]?.damage === 'Alto' ||
            false,
          overProcessed: false,
          incompatibleProducts: false,
        },
      };

      // Analizar cada foto con IA real en paralelo para mejor rendimiento
      const photoAnalyses = [];
      const analysisPromises = data.desiredPhotos.map(async photo => {
        try {
          // Debug logging removed for production
          const analysis = await analyzeDesiredPhoto(photo.id, photo.uri, diagnosis, data.clientId);

          if (analysis) {
            // Convertir el resultado de la IA al formato esperado por aggregateDesiredPhotoAnalyses
            return {
              general: {
                overallLevel: analysis.detectedLevel?.toString() || '7',
                overallTone: analysis.detectedTone || 'Rubio medio',
                technique: analysis.detectedTechnique || 'highlights',
                customTechnique: (analysis as any).customTechnique || '',
              },
              advanced: {
                contrast: (analysis as any).contrast || 'medium',
                direction: (analysis as any).direction || 'neutral',
                graysCoverage: (analysis as any).graysCoverage || 100,
                finalTexture: (analysis as any).finalTexture || 'natural',
                specialNotes: (analysis as any).specialNotes || '',
              },
              // Incluir datos de zonas si están disponibles
              zoneAnalysis: analysis.zoneAnalysis || null,
            };
          }
          return null;
        } catch (photoError) {
          logger.error(`[analyzeDesiredPhotos] Error analyzing photo ${photo.id}:`, photoError);
          return null;
        }
      });

      // Procesar análisis en paralelo
      const results = await Promise.allSettled(analysisPromises);

      // Filtrar resultados exitosos
      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          photoAnalyses.push(result.value);
        }
      });

      if (photoAnalyses.length === 0) {
        throw new Error('No se pudo analizar ninguna foto');
      }

      // Agregar los resultados de análisis individuales
      const aggregatedResult = aggregateDesiredPhotoAnalyses(photoAnalyses, data.desiredPhotos);

      // Marcar como resultado de IA
      aggregatedResult.isFromAI = true;
      aggregatedResult.confidence = 85; // Confianza alta para análisis IA real

      // Calcular análisis de viabilidad inmediatamente
      const viabilityAnalysis = analyzeServiceViability(
        diagnosis,
        aggregatedResult,
        data.zoneColorAnalysis
      );

      // Debug logging removed for production

      onUpdate({
        desiredAnalysisResult: aggregatedResult,
        viabilityAnalysis: viabilityAnalysis,
      });
      onSaveSilent?.(); // Use silent save for AI analysis

      return '✅ Referencias analizadas con IA. Revisa y ajusta según tu criterio';
    } catch (error: any) {
      logger.error('[analyzeDesiredPhotos] Error:', error);

      if (error.message === 'TIMEOUT_ERROR') {
        Alert.alert(
          'Análisis tardando más de lo normal',
          'El análisis de las fotos de referencia está tardando más de 30 segundos. ¿Qué deseas hacer?',
          [
            {
              text: 'Continuar esperando',
              onPress: () => analyzeDesiredPhotos(),
            },
            {
              text: 'Análisis manual',
              onPress: () => {
                onUpdate({ desiredMethod: 'manual' });
              },
              style: 'cancel',
            },
          ]
        );
        return;
      }

      // Fallback to mock data if AI fails
      Alert.alert(
        'Error de Conexión con IA',
        'No se pudo analizar las fotos con inteligencia artificial. ¿Deseas usar datos de ejemplo o intentar de nuevo?',
        [
          {
            text: 'Reintentar',
            onPress: () => analyzeDesiredPhotos(),
          },
          {
            text: 'Usar Ejemplo',
            onPress: () => {
              const mockResult: DesiredColorAnalysisResult = {
                general: {
                  overallLevel: '8/9',
                  overallTone: 'Rubio ceniza',
                  technique: 'balayage',
                  customTechnique: '',
                },
                zones: {
                  [HairZone.ROOTS]: {
                    zone: HairZone.ROOTS,
                    desiredLevel: 7,
                    desiredTone: 'Rubio oscuro',
                    desiredReflect: 'Ceniza',
                    coverage: 100,
                  },
                  [HairZone.MIDS]: {
                    zone: HairZone.MIDS,
                    desiredLevel: 8,
                    desiredTone: 'Rubio medio',
                    desiredReflect: 'Beige',
                    coverage: 80,
                  },
                  [HairZone.ENDS]: {
                    zone: HairZone.ENDS,
                    desiredLevel: 9,
                    desiredTone: 'Rubio claro',
                    desiredReflect: 'Platino',
                    coverage: 100,
                  },
                },
                advanced: {
                  contrast: 'medium',
                  direction: 'cooler',
                  graysCoverage: 100,
                  finalTexture: 'glossy',
                  specialNotes: '',
                },
                lifestyle: {
                  maintenanceLevel: MaintenanceLevel.MEDIUM,
                  avoidTones: [],
                  budgetLevel: BudgetLevel.STANDARD,
                },
                confidence: 50,
                isFromAI: false,
              };

              // Calcular viabilidad también para datos de ejemplo
              const diagnosis = {
                hairThickness: data.hairThickness,
                hairDensity: data.hairDensity,
                overallTone: data.overallTone,
                overallReflect: data.overallReflect || data.overallUndertone,
                averageLevel: data.zoneColorAnalysis?.[HairZone.ROOTS]?.level || 6,
                zoneAnalysis: data.zoneColorAnalysis,
                zonePhysicalAnalysis: data.zonePhysicalAnalysis,
              };

              const viabilityAnalysis = analyzeServiceViability(
                diagnosis,
                mockResult,
                data.zoneColorAnalysis
              );

              // Debug logging removed for production

              onUpdate({
                desiredAnalysisResult: mockResult,
                viabilityAnalysis: viabilityAnalysis,
              });
            },
          },
        ]
      );
    } finally {
      setIsAnalyzingDesired(false);
    }
  };

  const _handlePhotoCaptured = (uri: string, angle: any, quality: any) => {
    const message = handleDesiredCameraCapture(
      uri,
      angle,
      quality,
      data.desiredPhotos,
      photos => onUpdate({ desiredPhotos: photos }),
      currentDesiredPhotoTypeRef.current
    );

    if (message) {
      // Debug logging removed for production
    }
  };

  return (
    <>
      <AIResultNotification
        visible={showAINotification}
        onDismiss={() => setShowAINotification(false)}
        message="Análisis de color deseado completado"
        fieldsCount={aiFieldsCount}
        onViewResults={() => {
          if (scrollRef.current) {
            // Scroll to the analysis form with more noticeable offset
            scrollRef.current.scrollTo({ y: 600, animated: true });
            // Add haptic feedback
            setTimeout(() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }, 300);
          }
        }}
      />

      <ScrollView ref={scrollRef} style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.stepContainer}>
          <Text style={styles.stepTitle}>Resultado Deseado</Text>
          {data.client && (
            <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
          )}

          {/* Mostrar resumen del diagnóstico actual */}
          {data.hairThickness && (
            <DiagnosisSummary
              diagnosis={{
                thickness: data.hairThickness,
                density: data.hairDensity,
                tone: data.overallTone,
                reflect: data.overallReflect || data.overallUndertone,
                level: data.zoneColorAnalysis?.[HairZone.ROOTS]?.level,
                porosity: data.zonePhysicalAnalysis?.[HairZone.ROOTS]?.porosity,
                damage: data.zonePhysicalAnalysis?.[HairZone.ROOTS]?.damage,
                grayPercentage: data.zoneColorAnalysis?.[HairZone.ROOTS]?.grayPercentage,
              }}
            />
          )}

          {/* Method tabs */}
          <View style={styles.tabsContainer}>
            <TouchableOpacity
              style={[styles.tab, data.desiredMethod === 'ai' && styles.activeTab]}
              onPress={() => onUpdate({ desiredMethod: 'ai' })}
            >
              <Zap
                size={16}
                color={data.desiredMethod === 'ai' ? Colors.light.primary : Colors.light.gray}
              />
              <Text style={[styles.tabText, data.desiredMethod === 'ai' && styles.activeTabText]}>
                Con IA ✨
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, data.desiredMethod === 'manual' && styles.activeTab]}
              onPress={() => handleManualModeSelect()}
            >
              <Eye
                size={16}
                color={data.desiredMethod === 'manual' ? Colors.light.primary : Colors.light.gray}
              />
              <Text
                style={[styles.tabText, data.desiredMethod === 'manual' && styles.activeTabText]}
              >
                Manual
              </Text>
            </TouchableOpacity>
          </View>

          {/* Help text when AI data is present */}
          {data.desiredAnalysisResult?.isFromAI && data.desiredMethod === 'ai' && (
            <Text style={styles.helpText}>Los valores son editables • Toca para modificar</Text>
          )}

          {/* Photo capture section - Only show in AI mode */}
          {data.desiredMethod === 'ai' && (
            <>
              <Text style={styles.sectionTitle}>Referencias del color deseado (3-5 imágenes)</Text>

              <DesiredPhotoGallery
                photos={data.desiredPhotos}
                onAddPhoto={handleDesiredPhotoAdd}
                onCameraCapture={handleDesiredCameraNormal}
                onRemovePhoto={photoId => {
                  const updatedPhotos = data.desiredPhotos.filter(p => p.id !== photoId);
                  onUpdate({ desiredPhotos: updatedPhotos });
                }}
                maxPhotos={5}
              />

              {data.desiredPhotos.length > 0 && (
                <TouchableOpacity
                  style={[styles.analyzeButton, isAnalyzingDesired && styles.analyzeButtonDisabled]}
                  onPress={analyzeDesiredPhotos}
                  disabled={isAnalyzingDesired}
                >
                  {isAnalyzingDesired ? (
                    <>
                      <ActivityIndicator size="small" color="white" />
                      <Text style={styles.analyzeButtonText}>Analizando referencias...</Text>
                    </>
                  ) : (
                    <>
                      <Zap size={16} color="white" />
                      <Text style={styles.analyzeButtonText}>Analizar con IA</Text>
                    </>
                  )}
                </TouchableOpacity>
              )}
            </>
          )}

          {/* AI Status Indicator */}
          {data.desiredAnalysisResult && (
            <View
              style={[
                styles.aiStatusBadge,
                !data.desiredAnalysisResult.isFromAI && styles.aiStatusBadgeMock,
              ]}
            >
              {data.desiredAnalysisResult.isFromAI ? (
                <>
                  <Sparkles size={14} color={Colors.light.success} />
                  <Text style={styles.aiStatusText}>Análisis con IA</Text>
                </>
              ) : (
                <>
                  <AlertTriangle size={14} color={Colors.light.warning} />
                  <Text style={[styles.aiStatusText, styles.aiStatusTextMock]}>
                    Datos de ejemplo - Ajusta manualmente
                  </Text>
                </>
              )}
            </View>
          )}

          {/* Desired Color Analysis Form - Always visible */}
          <DesiredColorAnalysisForm
            analysisResult={data.desiredAnalysisResult}
            onAnalysisChange={newResult => {
              onUpdate({ desiredAnalysisResult: newResult });
            }}
            isFromAI={
              data.desiredMethod === 'ai' && (data.desiredAnalysisResult?.isFromAI || false)
            }
          />

          {/* Viability Analysis */}
          {data.desiredAnalysisResult && data.viabilityAnalysis && (
            <ViabilityIndicator
              analysis={data.viabilityAnalysis}
              loading={isRecalculatingViability}
            />
          )}

          {/* Continue Button */}
          {(data.desiredMethod === 'manual' || data.desiredAnalysisResult) && (
            <TouchableOpacity style={styles.continueButton} onPress={onNext}>
              <Text style={styles.continueButtonText}>Continuar a la Formulación</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 4,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    borderRadius: 12,
  },
  activeTab: {
    backgroundColor: Colors.light.background,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  tabText: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.light.gray,
  },
  activeTabText: {
    color: Colors.light.primary,
    fontWeight: '700',
  },
  helpText: {
    fontSize: 12,
    color: Colors.light.gray,
    textAlign: 'center',
    marginTop: -10,
    marginBottom: 15,
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.primary,
    borderRadius: 8,
    paddingVertical: 12,
    marginTop: 15,
    gap: 8,
  },
  analyzeButtonDisabled: {
    backgroundColor: Colors.light.gray,
    opacity: 0.6,
  },
  analyzeButtonText: {
    color: Colors.light.textLight,
    fontWeight: '600',
    fontSize: 16,
  },
  aiStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: Colors.light.success + '10',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  aiStatusBadgeMock: {
    backgroundColor: Colors.light.warning + '10',
  },
  aiStatusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.success,
  },
  aiStatusTextMock: {
    color: Colors.light.warning,
  },
  continueButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  continueButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
});
