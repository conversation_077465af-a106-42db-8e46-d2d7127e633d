import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import Colors from '@/constants/colors';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '@/stores/auth-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import {
  professionalHairColorBrands,
  Brand,
  searchBrands,
  getAllCountries,
} from '@/constants/reference-data/brands-data';

interface BrandSelection {
  brandId: string;
  brandName: string;
  selectedLines: string[];
}

export default function BrandsScreen() {
  const { preferredBrandLines, updatePreferredBrandLines } = useAuthStore();
  const { configuration: _configuration } = useSalonConfigStore();
  const [brandSelections, setBrandSelections] = useState<BrandSelection[]>(
    preferredBrandLines.map(bl => ({
      brandId: bl.brandId,
      brandName: professionalHairColorBrands.find(b => b.id === bl.brandId)?.name || bl.brandId,
      selectedLines: bl.selectedLines,
    }))
  );
  const [showCustomBrand, setShowCustomBrand] = useState(false);
  const [customBrand, setCustomBrand] = useState('');
  const [selectedBrandForLines, setSelectedBrandForLines] = useState<Brand | null>(null);
  const [tempSelectedLines, setTempSelectedLines] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCountryFilter, setSelectedCountryFilter] = useState('');
  const [filteredBrands, setFilteredBrands] = useState<Brand[]>(professionalHairColorBrands);

  const countries = getAllCountries();

  // Search and filter effect
  useEffect(() => {
    let results = searchBrands(searchQuery);

    if (selectedCountryFilter) {
      results = results.filter(brand => brand.country === selectedCountryFilter);
    }

    setFilteredBrands(results);
  }, [searchQuery, selectedCountryFilter]);

  const handleBrandSelect = (brand: Brand) => {
    // Check if brand is already selected
    const existingSelection = brandSelections.find(bs => bs.brandId === brand.id);

    if (existingSelection) {
      // Open lines selection with existing selections
      setSelectedBrandForLines(brand);
      setTempSelectedLines(existingSelection.selectedLines);
    } else {
      // Open lines selection for new brand
      setSelectedBrandForLines(brand);
      setTempSelectedLines([]);
    }
  };

  const toggleLineSelection = (lineId: string) => {
    setTempSelectedLines(prev => {
      if (prev.includes(lineId)) {
        return prev.filter(id => id !== lineId);
      }
      return [...prev, lineId];
    });
  };

  const saveBrandLineSelection = () => {
    if (!selectedBrandForLines || tempSelectedLines.length === 0) {
      Alert.alert(
        'Selecciona al menos una línea',
        'Debes seleccionar al menos una línea de coloración para continuar.'
      );
      return;
    }

    setBrandSelections(prev => {
      const existing = prev.findIndex(bs => bs.brandId === selectedBrandForLines.id);
      if (existing >= 0) {
        // Update existing selection
        const updated = [...prev];
        updated[existing] = {
          brandId: selectedBrandForLines.id,
          brandName: selectedBrandForLines.name,
          selectedLines: tempSelectedLines,
        };
        return updated;
      } else {
        // Add new selection
        return [
          ...prev,
          {
            brandId: selectedBrandForLines.id,
            brandName: selectedBrandForLines.name,
            selectedLines: tempSelectedLines,
          },
        ];
      }
    });

    setSelectedBrandForLines(null);
    setTempSelectedLines([]);
  };

  const removeBrandSelection = (brandId: string) => {
    setBrandSelections(prev => prev.filter(bs => bs.brandId !== brandId));
  };

  const handleAddCustomBrand = () => {
    if (customBrand.trim()) {
      // For custom brands, we'll add them without lines
      setBrandSelections(prev => [
        ...prev,
        {
          brandId: customBrand.trim().toLowerCase().replace(/\s+/g, '-'),
          brandName: customBrand.trim(),
          selectedLines: [],
        },
      ]);
      setCustomBrand('');
      setShowCustomBrand(false);
    }
  };

  const handleContinue = async () => {
    // Convert to auth store format
    const brandLines = brandSelections.map(selection => ({
      brandId: selection.brandId,
      selectedLines: selection.selectedLines,
    }));
    await updatePreferredBrandLines(brandLines);
    router.push('/onboarding/ready');
  };

  const handleSkip = () => {
    router.push('/onboarding/ready');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <OnboardingLayout
      currentStep={3}
      totalSteps={4}
      title="Marcas de Coloración"
      subtitle="Selecciona las marcas con las que trabajas habitualmente"
      onBack={handleBack}
    >
      <View style={styles.container}>
        {selectedBrandForLines ? (
          // Lines selection view
          <View style={styles.linesSection}>
            <View style={styles.brandHeader}>
              <TouchableOpacity
                onPress={() => {
                  setSelectedBrandForLines(null);
                  setTempSelectedLines([]);
                }}
                style={styles.backButton}
              >
                <Ionicons name="arrow-back" size={24} color={Colors.light.text} />
              </TouchableOpacity>
              <View style={styles.brandInfo}>
                <Text style={styles.brandTitle}>{selectedBrandForLines.name}</Text>
                <Text style={styles.brandCountry}>{selectedBrandForLines.country}</Text>
              </View>
            </View>

            <Text style={styles.linesPrompt}>
              Selecciona las líneas de coloración que utilizas:
            </Text>

            <ScrollView style={styles.linesContainer} showsVerticalScrollIndicator={false}>
              {selectedBrandForLines.lines.map(line => (
                <TouchableOpacity
                  key={line.id}
                  style={[
                    styles.lineItem,
                    tempSelectedLines.includes(line.id) && styles.lineItemSelected,
                  ]}
                  onPress={() => toggleLineSelection(line.id)}
                >
                  <View style={styles.lineContent}>
                    <Text
                      style={[
                        styles.lineName,
                        tempSelectedLines.includes(line.id) && styles.lineNameSelected,
                      ]}
                    >
                      {line.name}
                    </Text>
                    {line.description && (
                      <Text style={styles.lineDescription}>{line.description}</Text>
                    )}
                  </View>
                  {tempSelectedLines.includes(line.id) && (
                    <Ionicons name="checkmark-circle" size={20} color={Colors.light.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={[
                styles.saveButton,
                tempSelectedLines.length === 0 && styles.saveButtonDisabled,
              ]}
              onPress={saveBrandLineSelection}
              disabled={tempSelectedLines.length === 0}
            >
              <Text style={styles.saveButtonText}>
                Guardar Selección ({tempSelectedLines.length} línea
                {tempSelectedLines.length !== 1 ? 's' : ''})
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          // Brands selection view
          <View style={styles.section}>
            <Text style={styles.sectionDescription}>
              Esto nos ayudará a generar fórmulas más precisas para ti
            </Text>

            {/* Selected brands summary */}
            {brandSelections.length > 0 && (
              <View style={styles.selectedBrandsSection}>
                {brandSelections.map(selection => {
                  const brand = professionalHairColorBrands.find(b => b.id === selection.brandId);
                  return (
                    <View key={selection.brandId} style={styles.selectedBrandCard}>
                      <View style={styles.selectedBrandHeader}>
                        <View style={styles.selectedBrandInfo}>
                          <Text style={styles.selectedBrandName}>{selection.brandName}</Text>
                          <Text style={styles.selectedLinesCount}>
                            {selection.selectedLines.length} línea
                            {selection.selectedLines.length !== 1 ? 's' : ''} seleccionada
                            {selection.selectedLines.length !== 1 ? 's' : ''}
                          </Text>
                        </View>
                        <TouchableOpacity onPress={() => removeBrandSelection(selection.brandId)}>
                          <Ionicons
                            name="close-circle"
                            size={20}
                            color={Colors.light.textSecondary}
                          />
                        </TouchableOpacity>
                      </View>
                      {brand && selection.selectedLines.length > 0 && (
                        <View style={styles.selectedLinesPreview}>
                          {selection.selectedLines.slice(0, 3).map(lineId => {
                            const line = brand.lines.find(l => l.id === lineId);
                            return line ? (
                              <View key={lineId} style={styles.lineTag}>
                                <Text style={styles.lineTagText}>{line.name}</Text>
                              </View>
                            ) : null;
                          })}
                          {selection.selectedLines.length > 3 && (
                            <Text style={styles.moreLinesText}>
                              +{selection.selectedLines.length - 3} más
                            </Text>
                          )}
                        </View>
                      )}
                    </View>
                  );
                })}
              </View>
            )}

            {/* Search Bar */}
            <View style={styles.searchContainer}>
              <View style={styles.searchBar}>
                <Ionicons name="search" size={20} color={Colors.light.textSecondary} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Buscar marca..."
                  placeholderTextColor={Colors.light.textSecondary}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity onPress={() => setSearchQuery('')}>
                    <Ionicons name="close-circle" size={20} color={Colors.light.textSecondary} />
                  </TouchableOpacity>
                )}
              </View>
            </View>

            {/* Country Filters */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.countryFiltersContainer}
              contentContainerStyle={styles.countryFiltersContent}
            >
              <TouchableOpacity
                style={[styles.countryChip, !selectedCountryFilter && styles.countryChipActive]}
                onPress={() => setSelectedCountryFilter('')}
              >
                <Text
                  style={[
                    styles.countryChipText,
                    !selectedCountryFilter && styles.countryChipTextActive,
                  ]}
                >
                  Todos
                </Text>
              </TouchableOpacity>
              {countries.map(country => (
                <TouchableOpacity
                  key={country}
                  style={[
                    styles.countryChip,
                    selectedCountryFilter === country && styles.countryChipActive,
                  ]}
                  onPress={() =>
                    setSelectedCountryFilter(country === selectedCountryFilter ? '' : country)
                  }
                >
                  <Text
                    style={[
                      styles.countryChipText,
                      selectedCountryFilter === country && styles.countryChipTextActive,
                    ]}
                  >
                    {country}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Results counter */}
            <Text style={styles.resultsCount}>
              {filteredBrands.length} marca
              {filteredBrands.length !== 1 ? 's' : ''} encontrada
              {filteredBrands.length !== 1 ? 's' : ''}
            </Text>

            <ScrollView style={styles.brandsContainer} showsVerticalScrollIndicator={false}>
              <View style={styles.brandsGrid}>
                {filteredBrands.map(brand => {
                  const isSelected = brandSelections.some(bs => bs.brandId === brand.id);
                  return (
                    <TouchableOpacity
                      key={brand.id}
                      style={[styles.brandCard, isSelected && styles.brandCardSelected]}
                      onPress={() => handleBrandSelect(brand)}
                    >
                      <View style={styles.brandCardContent}>
                        <Text style={[styles.brandName, isSelected && styles.brandNameSelected]}>
                          {brand.name}
                        </Text>
                        <Text style={styles.brandLines}>
                          {brand.lines.length} líneas disponibles
                        </Text>
                      </View>
                      {isSelected && (
                        <Ionicons name="checkmark-circle" size={20} color={Colors.light.primary} />
                      )}
                    </TouchableOpacity>
                  );
                })}

                {/* Custom brands */}
                {brandSelections
                  .filter(bs => !professionalHairColorBrands.find(b => b.id === bs.brandId))
                  .map(selection => (
                    <TouchableOpacity
                      key={selection.brandId}
                      style={[styles.brandCard, styles.brandCardSelected]}
                      onPress={() => removeBrandSelection(selection.brandId)}
                    >
                      <View style={styles.brandCardContent}>
                        <Text style={[styles.brandName, styles.brandNameSelected]}>
                          {selection.brandName}
                        </Text>
                        <Text style={styles.brandLines}>Marca personalizada</Text>
                      </View>
                      <Ionicons name="checkmark-circle" size={20} color={Colors.light.primary} />
                    </TouchableOpacity>
                  ))}

                {/* Add custom brand button */}
                <TouchableOpacity
                  style={[styles.brandCard, styles.addBrandCard]}
                  onPress={() => setShowCustomBrand(true)}
                >
                  <Ionicons name="add" size={24} color={Colors.light.primary} />
                  <Text style={styles.addBrandText}>Agregar marca</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>

            {showCustomBrand && (
              <View style={styles.customBrandContainer}>
                <TextInput
                  style={styles.customBrandInput}
                  value={customBrand}
                  onChangeText={setCustomBrand}
                  placeholder="Nombre de la marca"
                  placeholderTextColor={Colors.light.textSecondary}
                  autoFocus
                />
                <TouchableOpacity style={styles.customBrandButton} onPress={handleAddCustomBrand}>
                  <Ionicons name="checkmark" size={20} color="white" />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.customBrandCancelButton}
                  onPress={() => {
                    setShowCustomBrand(false);
                    setCustomBrand('');
                  }}
                >
                  <Ionicons name="close" size={20} color={Colors.light.textSecondary} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        <View style={styles.footer}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleContinue}>
            <Text style={styles.primaryButtonText}>
              {brandSelections.length > 0 ? 'Continuar' : 'Continuar sin marcas'}
            </Text>
            <Ionicons name="arrow-forward" size={20} color="white" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
            <Text style={styles.skipButtonText}>Configurar más tarde</Text>
          </TouchableOpacity>
        </View>
      </View>
    </OnboardingLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    flex: 1,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 20,
  },
  brandsContainer: {
    flex: 1,
  },
  brandsGrid: {
    gap: 12,
  },
  brandCard: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: Colors.common.transparent,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
    width: '100%',
  },
  brandCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: `${Colors.light.primary}10`,
  },
  brandCardContent: {
    flex: 1,
  },
  brandName: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '600',
    marginBottom: 4,
  },
  brandNameSelected: {
    color: Colors.light.primary,
  },
  brandLines: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  addBrandCard: {
    borderStyle: 'dashed',
    borderColor: Colors.light.primary,
    borderWidth: 2,
    backgroundColor: Colors.common.transparent,
  },
  addBrandText: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '500',
  },
  customBrandContainer: {
    flexDirection: 'row',
    marginTop: 16,
    gap: 8,
  },
  customBrandInput: {
    flex: 1,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: Colors.light.text,
  },
  customBrandButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customBrandCancelButton: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedBrandsSection: {
    marginBottom: 20,
  },
  selectedBrandCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  selectedBrandHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  selectedBrandInfo: {
    flex: 1,
  },
  selectedBrandName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  selectedLinesCount: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  selectedLinesPreview: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    alignItems: 'center',
  },
  lineTag: {
    backgroundColor: Colors.light.background,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 8,
  },
  lineTagText: {
    fontSize: 12,
    color: Colors.light.text,
  },
  moreLinesText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
  linesSection: {
    flex: 1,
  },
  brandHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 12,
  },
  backButton: {
    padding: 4,
  },
  brandInfo: {
    flex: 1,
  },
  brandTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 2,
  },
  brandCountry: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  linesPrompt: {
    fontSize: 16,
    color: Colors.light.text,
    marginBottom: 16,
  },
  linesContainer: {
    flex: 1,
    marginBottom: 20,
  },
  lineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: Colors.common.transparent,
  },
  lineItemSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: `${Colors.light.primary}10`,
  },
  lineContent: {
    flex: 1,
  },
  lineName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 4,
  },
  lineNameSelected: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  lineDescription: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  saveButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    gap: 12,
    paddingTop: 20,
  },
  primaryButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  primaryButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
  skipButton: {
    alignItems: 'center',
    padding: 12,
  },
  skipButtonText: {
    color: Colors.light.textSecondary,
    fontSize: 14,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  countryFiltersContainer: {
    marginBottom: 16,
  },
  countryFiltersContent: {
    paddingRight: 16,
    gap: 8,
  },
  countryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.light.surface,
    marginRight: 8,
  },
  countryChipActive: {
    backgroundColor: Colors.light.primary,
  },
  countryChipText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  countryChipTextActive: {
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  resultsCount: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 12,
  },
});
