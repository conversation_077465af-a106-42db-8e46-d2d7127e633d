// Test script to verify new brands are properly loaded
const fs = require('fs');

// Read the brands file
const content = fs.readFileSync('./constants/reference-data/brands-data.ts', 'utf8');

// Extract brand names using a better pattern
const brandPattern = /{\s*id:\s*'[^']+',\s*name:\s*'([^']+)',\s*country:/g;
const brandNames = [];
let match;

while ((match = brandPattern.exec(content)) !== null) {
  brandNames.push(match[1]);
}

// Remove duplicates and sort
const uniqueBrands = [...new Set(brandNames)].sort();

console.log(`Total brands found: ${uniqueBrands.length}`);
console.log('\nNew brands that should be included:');

const newBrands = [
  'Pulp Riot',
  'Manic Panic Professional', 
  'Olaplex',
  'Uberliss',
  'Guy Tang',
  'Arctic Fox Professional',
  'Schwarzkopf Igora Zero',
  'Wella Freelights',
  'Redken Color Gels',
  'K18',
  'Brazilian Blowout Professional',
  'Bumble and bumble Color'
];

newBrands.forEach(brand => {
  const found = uniqueBrands.includes(brand);
  console.log(`${found ? '✅' : '❌'} ${brand}`);
});

console.log('\nFirst 20 brands in the system:');
uniqueBrands.slice(0, 20).forEach((brand, index) => {
  console.log(`${index + 1}. ${brand}`);
});

console.log('\nLast 10 brands in the system:');
uniqueBrands.slice(-10).forEach((brand, index) => {
  console.log(`${uniqueBrands.length - 9 + index}. ${brand}`);
});
