// Modern vibrant color palette with pink, coral and purple tones
// Bright Pink Crayola, <PERSON><PERSON>, <PERSON><PERSON>cous, <PERSON>, and Fairy Tale colors
// Updated for WCAG 2.1 AA compliance and contemporary salon aesthetics
// EXACT PALETTE COLORS - NO ALPHA CHANNEL
const brightPinkCrayola = '#ef798a'; // Primary brand color
const melon = '#f7a9a8'; // Light/background tones
const glaucous = '#7d82b8'; // Accent/info blue-purple
const finn = '#613f75'; // Dark purple for text/emphasis
const fairyTale = '#e5c3d1'; // Soft pink for backgrounds

// Backwards compatibility aliases
const premiumBlack = finn;
const roseLight = melon;
const coralSecondary = fairyTale;
const _beigeLight = fairyTale;
const _creamWhite = '#FFFFFF';
const successGreen = glaucous;
const warmGray = finn;
const warningCoral = melon;
const errorRose = brightPinkCrayola;
const infoBlue = glaucous;

export default {
  common: {
    transparent: 'transparent',
    black: '#000000',
    shadowColor: '#000000',
  },
  light: {
    // Text colors
    text: finn, // Dark purple for primary text
    textSecondary: warmGray,
    textLight: '#FFFFFF',

    // Background colors
    background: '#FFFFFF',
    backgroundSecondary: '#F8F8FF', // Very light purple-tinted background
    backgroundDark: premiumBlack,

    // Brand colors - Balanced distribution like reference images
    primary: finn, // Dark purple as main brand color
    primaryDark: finn, // Keep consistent
    primaryLight: glaucous, // Blue-purple as light primary
    secondary: brightPinkCrayola, // Bright pink as secondary accent
    accent: melon, // Coral as accent

    // UI elements
    card: '#FFFFFF',
    cardDark: premiumBlack,
    surface: '#F8F8FF', // Light surfaces with purple hint
    border: '#E0E0E0', // Neutral borders
    borderLight: '#F0F0F0', // Light neutral borders

    // Navigation
    tabIconDefault: warmGray,
    tabIconSelected: finn, // Dark purple for selected tabs
    tint: finn, // Dark purple tint

    // Status colors
    success: successGreen,
    warning: warningCoral,
    warningBackground: 'rgba(247, 169, 168, 0.2)', // Melon with transparency
    warningBorder: melon, // Melon border
    warningText: finn, // Dark text
    error: errorRose,
    info: infoBlue,
    danger: errorRose,

    // Grays
    gray: warmGray,
    grayLight: '#C4C4C4',
    lightGray: '#e5c3d1', // Fairy Tale
    darkGray: '#4A4A4A',
    placeholderGray: fairyTale, // Soft placeholder
    disabledGray: '#E0E0E0',
    redButton: brightPinkCrayola, // Vibrant button color

    // Special UI states
    notification: coralSecondary,
    highlight: roseLight,

    // AI Analysis specific colors
    aiProcessing: glaucous, // Blue-purple for processing
    aiSuccess: glaucous, // Blue-purple for success
    aiWarning: melon, // Coral for warnings
    aiError: brightPinkCrayola, // Pink for errors

    // Privacy and security colors
    privacy: successGreen,
    security: infoBlue,

    // Quality indicators
    qualityExcellent: glaucous, // Blue-purple for excellent
    qualityGood: finn, // Dark purple for good
    qualityPoor: brightPinkCrayola, // Pink for poor

    // Progress indicators
    progressBackground: '#F0F0F5', // Neutral light background
    progressFill: finn, // Dark purple for progress
    progressBarBackground: '#F0F0F5', // Neutral progress track
    progressBarFill: glaucous, // Blue-purple fill

    // Timeline and UI specific colors
    timelineBackground: fairyTale, // Timeline background
    pauseButton: melon, // Pause button color
    disabledText: '#ccc',
    whiteTransparent80: 'rgba(255,255,255,0.8)',

    // Shadows
    shadowColor: '#000000',
    shadowLight: 'rgba(0, 0, 0, 0.05)',
    shadowMedium: 'rgba(0, 0, 0, 0.1)',
    blackOpacity20: 'rgba(0, 0, 0, 0.2)',

    // White with opacity variations (common in overlays/backgrounds)
    backgroundWithOpacity: 'rgba(255, 255, 255, 0.3)',
    rippleWhite: 'rgba(255,255,255,0.3)',
    rippleRed: 'rgba(255, 107, 107, 0.125)',
    backgroundOpacity20: 'rgba(255, 255, 255, 0.2)',
    backgroundOpacity50: 'rgba(255, 255, 255, 0.5)',
    backgroundOpacity70: 'rgba(255, 255, 255, 0.7)',
    backgroundOpacity90: 'rgba(255, 255, 255, 0.9)',
    backgroundOpacity95: 'rgba(255, 255, 255, 0.95)',
    loadingBackground: 'rgba(248, 250, 252, 0.95)',
    brushPurple: glaucous, // Blue-purple brush
    sparkleYellow: melon, // Coral sparkles
    zonePrimary: brightPinkCrayola, // Primary zone
    zoneSecondary: glaucous, // Secondary zone

    // Modal and overlay colors
    modalOverlay: 'rgba(0, 0, 0, 0.5)',
    borderTransparent: 'rgba(0, 0, 0, 0.05)',
    borderGray: '#E0E0E0', // Neutral borders

    // Transparency variations (for semantic use)
    primaryTransparent10: 'rgba(97, 63, 117, 0.1)', // finn (new primary)
    primaryTransparent15: 'rgba(97, 63, 117, 0.15)', // finn (new primary)
    primaryTransparent20: 'rgba(97, 63, 117, 0.2)', // finn (new primary)
    secondaryTransparent10: 'rgba(239, 121, 138, 0.1)', // bright-pink-crayola (new secondary)
    secondaryTransparent15: 'rgba(239, 121, 138, 0.15)', // bright-pink-crayola (new secondary)
    secondaryTransparent20: 'rgba(239, 121, 138, 0.2)', // bright-pink-crayola (new secondary)
    accentTransparent15: 'rgba(247, 169, 168, 0.15)', // melon (new accent)
    successTransparent15: 'rgba(125, 130, 184, 0.15)', // glaucous transparency
    warningTransparent10: 'rgba(247, 169, 168, 0.1)', // warningCoral
    warningTransparent15: 'rgba(247, 169, 168, 0.15)', // warningCoral
    errorTransparent10: 'rgba(239, 121, 138, 0.1)', // errorRose
    errorTransparent20: 'rgba(239, 121, 138, 0.2)', // errorRose
    infoTransparent15: 'rgba(125, 130, 184, 0.15)', // infoBlue (Glaucous)
  },
};
